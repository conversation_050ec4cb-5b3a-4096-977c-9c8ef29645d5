"""
✅ ValidationManager - Handles all validation logic and operations

This manager is responsible for:
- Real-time alias validation with visual indicators
- Business case and component name validation
- Uniqueness checking for aliases and names
- Providing validation feedback to users
- Integration with alias generation system

Part of the UIManager refactoring to improve code organization and maintainability.
"""

import tkinter as tk
from typing import Optional, List, Dict
import sqlite3


class ValidationManager:
    """Manages all validation logic and operations"""
    
    def __init__(self, database_manager=None):
        """Initialize ValidationManager with optional database manager"""
        print('[DEBUG] ValidationManager.__init__ called')
        
        self.database_manager = database_manager
        
        # Validation rules
        self.min_alias_length = 2
        self.max_alias_length = 50
        self.min_name_length = 1
        self.max_name_length = 100
        
        # Validation indicators
        self.validation_symbols = {
            'valid': '✓',
            'invalid': '✗',
            'duplicate': '!',
            'unknown': '?'
        }
        
        self.validation_colors = {
            'valid': 'green',
            'invalid': 'red',
            'duplicate': 'orange',
            'unknown': 'gray'
        }
    
    def validate_alias_realtime(self, alias: str, indicator: tk.Label):
        """Validate alias in real-time and update visual indicator"""
        print(f'[DEBUG] ValidationManager.validate_alias_realtime called with alias={alias}')
        
        try:
            from utils.alias_generator import AliasGenerator
            
            generator = AliasGenerator()
            
            # Basic validation
            if not generator.validate_alias(alias):
                self._update_indicator(indicator, 'invalid')
                return False
            
            # Check uniqueness
            try:
                if self.is_alias_unique(alias):
                    self._update_indicator(indicator, 'valid')
                    return True
                else:
                    self._update_indicator(indicator, 'duplicate')
                    return False
            except Exception as e:
                print(f'[WARNING] Could not check alias uniqueness: {e}')
                self._update_indicator(indicator, 'unknown')
                return False
                
        except Exception as e:
            print(f'[ERROR] Alias validation failed: {e}')
            self._update_indicator(indicator, 'unknown')
            return False
    
    def validate_business_case_name(self, name: str) -> bool:
        """Validate business case name"""
        print(f'[DEBUG] ValidationManager.validate_business_case_name called with name={name}')
        
        # Basic validation
        if not self._validate_name_basic(name):
            return False
        
        # Check uniqueness
        try:
            if self.database_manager:
                more_ops = self.database_manager.get_more_operations()
                business_cases = more_ops.read_all_business_cases()
                
                for bc in business_cases:
                    if bc.get('name', '').lower() == name.lower():
                        print(f'[DEBUG] Business case name already exists: {name}')
                        return False
            
            return True
            
        except Exception as e:
            print(f'[ERROR] Business case name validation failed: {e}')
            return False
    
    def validate_component_name(self, name: str, business_case_id: Optional[int] = None) -> bool:
        """Validate component name, optionally within a specific business case"""
        print(f'[DEBUG] ValidationManager.validate_component_name called with name={name}, business_case_id={business_case_id}')
        
        # Basic validation
        if not self._validate_name_basic(name):
            return False
        
        # Check uniqueness within business case if specified
        if business_case_id and self.database_manager:
            try:
                more_ops = self.database_manager.get_more_operations()
                components = more_ops.read_components_for_business_case(business_case_id)
                
                for comp in components:
                    if comp.get('name', '').lower() == name.lower():
                        print(f'[DEBUG] Component name already exists in business case {business_case_id}: {name}')
                        return False
                        
            except Exception as e:
                print(f'[ERROR] Component name validation failed: {e}')
                return False
        
        return True
    
    def is_alias_unique(self, alias: str, exclude_id: Optional[int] = None) -> bool:
        """Check if alias is unique in the database"""
        print(f'[DEBUG] ValidationManager.is_alias_unique called with alias={alias}, exclude_id={exclude_id}')
        
        try:
            with sqlite3.connect("source/DB/clipsmore_db.db") as conn:
                cursor = conn.cursor()
                
                if exclude_id:
                    cursor.execute(
                        "SELECT COUNT(*) FROM clipsmore_tbl WHERE LOWER(alias) = LOWER(?) AND transaction_id != ?", 
                        (alias, exclude_id)
                    )
                else:
                    cursor.execute(
                        "SELECT COUNT(*) FROM clipsmore_tbl WHERE LOWER(alias) = LOWER(?)", 
                        (alias,)
                    )
                
                count = cursor.fetchone()[0]
                is_unique = count == 0
                
                print(f'[DEBUG] Alias uniqueness check: {alias} -> {is_unique} (count: {count})')
                return is_unique
                
        except Exception as e:
            print(f'[ERROR] Alias uniqueness check failed: {e}')
            return False
    
    def is_business_case_name_unique(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """Check if business case name is unique"""
        print(f'[DEBUG] ValidationManager.is_business_case_name_unique called with name={name}, exclude_id={exclude_id}')
        
        try:
            if self.database_manager:
                more_ops = self.database_manager.get_more_operations()
                business_cases = more_ops.read_all_business_cases()
                
                for bc in business_cases:
                    if bc.get('id') != exclude_id and bc.get('name', '').lower() == name.lower():
                        return False
                
                return True
            else:
                print('[WARNING] No database manager available for business case name uniqueness check')
                return True
                
        except Exception as e:
            print(f'[ERROR] Business case name uniqueness check failed: {e}')
            return False
    
    def is_component_name_unique_in_business_case(self, name: str, business_case_id: int, exclude_id: Optional[int] = None) -> bool:
        """Check if component name is unique within a business case"""
        print(f'[DEBUG] ValidationManager.is_component_name_unique_in_business_case called with name={name}, business_case_id={business_case_id}, exclude_id={exclude_id}')
        
        try:
            if self.database_manager:
                more_ops = self.database_manager.get_more_operations()
                components = more_ops.read_components_for_business_case(business_case_id)
                
                for comp in components:
                    if comp.get('id') != exclude_id and comp.get('name', '').lower() == name.lower():
                        return False
                
                return True
            else:
                print('[WARNING] No database manager available for component name uniqueness check')
                return True
                
        except Exception as e:
            print(f'[ERROR] Component name uniqueness check failed: {e}')
            return False
    
    def validate_alias_format(self, alias: str) -> Dict[str, any]:
        """Validate alias format and return detailed results"""
        print(f'[DEBUG] ValidationManager.validate_alias_format called with alias={alias}')
        
        result = {
            'valid': False,
            'errors': [],
            'warnings': []
        }
        
        try:
            from utils.alias_generator import AliasGenerator
            generator = AliasGenerator()
            
            # Length validation
            if len(alias) < self.min_alias_length:
                result['errors'].append(f"Alias must be at least {self.min_alias_length} characters long")
            elif len(alias) > self.max_alias_length:
                result['errors'].append(f"Alias must be no more than {self.max_alias_length} characters long")
            
            # Format validation using AliasGenerator
            if not generator.validate_alias(alias):
                result['errors'].append("Alias must start with a letter and contain only letters, numbers, underscores, and hyphens")
            
            # Uniqueness check
            if not self.is_alias_unique(alias):
                result['warnings'].append("Alias is already in use")
            
            # Set overall validity
            result['valid'] = len(result['errors']) == 0
            
            print(f'[DEBUG] Alias validation result: {result}')
            return result
            
        except Exception as e:
            print(f'[ERROR] Alias format validation failed: {e}')
            result['errors'].append(f"Validation error: {str(e)}")
            return result
    
    def get_validation_suggestions(self, alias: str, count: int = 3) -> List[str]:
        """Get validation suggestions for an invalid or duplicate alias"""
        print(f'[DEBUG] ValidationManager.get_validation_suggestions called with alias={alias}, count={count}')
        
        try:
            from utils.alias_generator import AliasGenerator
            
            # Get existing aliases
            existing_aliases = []
            if self.database_manager:
                existing_aliases = self.database_manager.get_existing_aliases()
            
            generator = AliasGenerator()
            generator.set_existing_aliases(existing_aliases)
            
            suggestions = generator.suggest_alternatives(alias, count)
            print(f'[DEBUG] Generated {len(suggestions)} suggestions: {suggestions}')
            
            return suggestions
            
        except Exception as e:
            print(f'[ERROR] Failed to generate validation suggestions: {e}')
            return []
    
    def _validate_name_basic(self, name: str) -> bool:
        """Basic validation for names (business cases, components)"""
        if not name or not name.strip():
            return False
        
        name = name.strip()
        
        if len(name) < self.min_name_length:
            return False
        
        if len(name) > self.max_name_length:
            return False
        
        # Check for invalid characters (basic check)
        invalid_chars = ['<', '>', '|', ':', '*', '?', '"', '/', '\\']
        for char in invalid_chars:
            if char in name:
                return False
        
        return True
    
    def _update_indicator(self, indicator: tk.Label, status: str):
        """Update validation indicator with appropriate symbol and color"""
        if status in self.validation_symbols and status in self.validation_colors:
            indicator.config(
                text=self.validation_symbols[status],
                fg=self.validation_colors[status]
            )
        else:
            indicator.config(
                text=self.validation_symbols['unknown'],
                fg=self.validation_colors['unknown']
            )
    
    def create_validation_indicator(self, parent: tk.Widget, initial_status: str = 'unknown') -> tk.Label:
        """Create a validation indicator label"""
        print('[DEBUG] ValidationManager.create_validation_indicator called')
        
        indicator = tk.Label(parent, 
                           text=self.validation_symbols[initial_status],
                           fg=self.validation_colors[initial_status],
                           font=("Arial", 9, "bold"), 
                           width=2)
        
        return indicator
