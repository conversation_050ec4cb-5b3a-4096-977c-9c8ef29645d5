"""
🌳 TreeManager - Handles all tree-related operations and UI components

This manager is responsible for:
- Creating and managing the business case/component tree view
- Handling tree events (clicks, drag & drop, context menus)
- Managing clip buttons associated with tree items
- Providing tree filtering and search functionality
- Handling tree item operations (move, copy, etc.)

Part of the UIManager refactoring to improve code organization and maintainability.
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Optional, Tuple, Any


class TreeManager:
    """Manages all tree-related operations and UI components"""
    
    def __init__(self, parent: tk.Widget, theme_manager, database_manager):
        """Initialize TreeManager with required dependencies"""
        print('[DEBUG] TreeManager.__init__ called')
        
        self.parent = parent
        self.theme_manager = theme_manager
        self.database_manager = database_manager
        
        # UI components
        self.tree = None
        self.clip_buttons_frame = None
        self.buttons_canvas = None
        self.buttons_scrollbar = None
        self.buttons_scrollable_frame = None
        
        # State tracking
        self.tree_items = {}
        self.clip_button_widgets = {}
        self.drag_source = None
        self.context_menu = None
        
        # Search functionality
        self.search_var = None
    
    def create_tree_interface(self) -> Tuple[ttk.Treeview, tk.Frame]:
        """Create and return the tree interface components"""
        print('[DEBUG] TreeManager.create_tree_interface called')
        
        # Create frame to hold tree and button area
        tree_frame = tk.Frame(self.parent, bg=self.theme_manager.bg_color)
        
        # Create treeview for business cases and components
        self.tree = ttk.Treeview(tree_frame)
        self.tree.pack(side=tk.LEFT, expand=False, fill='y')
        
        # Configure tree to show only names (no extra columns)
        self.tree['columns'] = ()
        self.tree.column("#0", width=300, minwidth=200)  # Name column
        self.tree.heading("#0", text='Business Cases & Components')
        
        # Create frame for clip buttons
        self.clip_buttons_frame = tk.Frame(tree_frame, bg=self.theme_manager.bg_color)
        self.clip_buttons_frame.pack(side=tk.LEFT, fill='both', expand=True, padx=(10, 0))
        
        # Create canvas and scrollbar for clip buttons
        self.buttons_canvas = tk.Canvas(self.clip_buttons_frame, bg=self.theme_manager.bg_color)
        self.buttons_scrollbar = ttk.Scrollbar(self.clip_buttons_frame, orient="vertical", 
                                             command=self.buttons_canvas.yview)
        self.buttons_scrollable_frame = tk.Frame(self.buttons_canvas, bg=self.theme_manager.bg_color)
        
        self.buttons_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.buttons_canvas.configure(scrollregion=self.buttons_canvas.bbox("all"))
        )
        
        self.buttons_canvas.create_window((0, 0), window=self.buttons_scrollable_frame, anchor="nw")
        self.buttons_canvas.configure(yscrollcommand=self.buttons_scrollbar.set)
        
        self.buttons_canvas.pack(side="left", fill="both", expand=True)
        self.buttons_scrollbar.pack(side="right", fill="y")
        
        # Store button widgets for management
        self.clip_button_widgets = {}
        
        # Bind tree events
        self._bind_tree_events()
        
        return self.tree, tree_frame
    
    def _bind_tree_events(self):
        """Bind all tree event handlers"""
        print('[DEBUG] TreeManager._bind_tree_events called')
        
        # Enable drag-and-drop functionality and clip button handling
        self.tree.bind('<ButtonRelease-1>', self.on_tree_item_click)
        self.tree.bind('<B1-Motion>', self.on_tree_item_drag)
        self.tree.bind('<ButtonPress-1>', self.on_tree_item_press)
        self.tree.bind('<Button-3>', self.show_context_menu)
        self.tree.bind('<Double-1>', self.on_tree_double_click)
    
    def refresh_tree(self):
        """Refresh the tree with current database data"""
        print('[DEBUG] TreeManager.refresh_tree called')
        
        # Clear all items from the tree
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.tree_items = {}
        
        # Clear all clip button widgets
        for widget in self.buttons_scrollable_frame.winfo_children():
            widget.destroy()
        self.clip_button_widgets = {}
        
        try:
            # Get all business cases and their components from the DB
            more_ops = self.database_manager.get_more_operations()
            business_cases = more_ops.read_all_business_cases()
            
            # Get clip assignments for enhanced tree display
            enhanced_ops = self.database_manager.get_enhanced_operations()
            
            current_y_position = 0
            
            for bc in business_cases:
                bc_id = bc['id']
                bc_name = bc['name']
                bc_item = self.tree.insert('', 'end', text=bc_name, values=('Business Case',))
                self.tree_items[bc_id] = bc_item
                
                # Get clip assignments for this business case (without component)
                try:
                    bc_assignments = enhanced_ops.get_assignments_by_business_case(bc_id)
                    bc_only_assignments = [a for a in bc_assignments if not a.get('more_comp_id')]
                    
                    # Create clip buttons for business case assignments
                    if bc_only_assignments:
                        current_y_position = self.create_clip_buttons_for_item(
                            bc_name, bc_only_assignments, current_y_position
                        )
                        
                except Exception as e:
                    print(f'[WARNING] Could not load assignments for business case {bc_name}: {e}')
                
                # Get components for this business case
                components = more_ops.read_components_for_business_case(bc_id)
                print(f'[DEBUG] Business case {bc_name} (ID: {bc_id}) has {len(components)} components: {components}')
                
                for comp in components:
                    comp_id = comp['id']
                    comp_name = comp['name']
                    print(f'[DEBUG] Adding component to tree: {comp_name} (ID: {comp_id})')
                    comp_item = self.tree.insert(bc_item, 'end', text=comp_name, values=('Component',))
                    # Add debug statement to verify values are set correctly
                    print(f'[DEBUG] Created component item: {comp_item}, text={comp_name}, values=("Component",)')
                    print(f'[DEBUG] Component inserted with tree item ID: {comp_item}')
                    
                    # Get clip assignments for this component
                    try:
                        comp_assignments = enhanced_ops.get_assignments_by_component(comp_id)
                        
                        # Create clip buttons for component assignments
                        if comp_assignments:
                            current_y_position = self.create_clip_buttons_for_item(
                                f"{bc_name} → {comp_name}", comp_assignments, current_y_position
                            )
                            
                    except Exception as e:
                        print(f'[WARNING] Could not load assignments for component {comp_name}: {e}')
                
                # Automatically expand all business case nodes to show components
                children = self.tree.get_children(bc_item)
                if children:
                    self.tree.item(bc_item, open=True)
                    print(f'[DEBUG] Expanded tree item: {bc_name}')
            
            # Final verification of entire tree structure
            all_items = self.tree.get_children()
            print(f'[DEBUG] Tree refresh complete. Total business cases in tree: {len(all_items)}')
            
        except Exception as e:
            print(f'[ERROR] Error loading tree: {e}')
            # Show error in parent if available
            if hasattr(self.parent, 'show_error'):
                self.parent.show_error(f"Error loading tree: {e}")
    
    def create_clip_buttons_for_item(self, item_name: str, assignments: List[Dict], y_position: int) -> int:
        """Create clip buttons for a business case or component"""
        print(f'[DEBUG] TreeManager.create_clip_buttons_for_item called for {item_name} with {len(assignments)} assignments')

        # Configure column to expand horizontally
        self.buttons_scrollable_frame.grid_columnconfigure(0, weight=1)

        # Create a label for the item name
        item_label = tk.Label(self.buttons_scrollable_frame,
                             text=f"{item_name}:",
                             bg=self.theme_manager.bg_color,
                             fg=self.theme_manager.fg_color,
                             font=('Arial', 10, 'bold'),
                             anchor='w')
        item_label.grid(row=y_position, column=0, sticky='ew', padx=5, pady=(10, 2))
        y_position += 1

        # Create frame for buttons in this row - now stretches horizontally
        buttons_frame = tk.Frame(self.buttons_scrollable_frame, bg=self.theme_manager.bg_color)
        buttons_frame.grid(row=y_position, column=0, sticky='ew', padx=20, pady=2)
        
        # Create buttons for each assignment with improved horizontal distribution
        for i, assignment in enumerate(assignments):
            alias = assignment.get('alias', 'Unknown')

            # Configure column for this button to expand proportionally
            buttons_frame.grid_columnconfigure(i, weight=1)

            # Create the clip button without fixed width for better stretching
            clip_button = tk.Button(buttons_frame,
                                  text=alias,
                                  command=lambda a=alias: self._copy_clip_by_alias(a),
                                  bg="#4CAF50", fg="white",
                                  activebackground="#45a049", activeforeground="white",
                                  font=('Arial', 9, 'bold'),
                                  relief='raised',
                                  bd=2,
                                  cursor='hand2')

            # Use grid instead of pack for better horizontal stretching
            clip_button.grid(row=0, column=i, sticky='ew', padx=2, pady=1)
            
            # Store button reference for management
            button_key = f"{item_name}_{alias}"
            self.clip_button_widgets[button_key] = clip_button
            
            print(f'[DEBUG] Created clip button: {alias} for {item_name}')
        
        return y_position + 1
    
    def _copy_clip_by_alias(self, alias: str):
        """Copy clip content to clipboard by alias"""
        print(f'[DEBUG] TreeManager._copy_clip_by_alias called with alias={alias}')
        
        try:
            import sqlite3
            
            # Get clip content by alias using the view
            with sqlite3.connect("source/DB/clipsmore_db.db") as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT clip_content
                    FROM clipsmore_vw
                    WHERE alias = ?
                    LIMIT 1
                """, (alias,))
                
                result = cursor.fetchone()
                if result:
                    content = result[0]
                    if content:
                        # Get root window for clipboard operations
                        root = self.parent
                        while root.master:
                            root = root.master
                        
                        root.clipboard_clear()
                        root.clipboard_append(content)
                        print(f'[DEBUG] Copied content for alias: {alias}')
                        
                        # Show brief feedback
                        messagebox.showinfo("Copied", f"Content for '{alias}' copied to clipboard!")
                    else:
                        print(f'[WARNING] No content found for alias: {alias}')
                        messagebox.showwarning("No Content", f"No content found for alias: {alias}")
                else:
                    print(f'[WARNING] Alias not found: {alias}')
                    messagebox.showwarning("Not Found", f"Alias not found: {alias}")
                    
        except Exception as e:
            print(f'[ERROR] Failed to copy clip by alias: {e}')
            messagebox.showerror("Error", f"Failed to copy clip: {e}")
    
    def filter_tree(self, query: str):
        """Filter treeview based on search query"""
        print(f'[DEBUG] TreeManager.filter_tree called with query={query}')
        
        query_lower = query.lower()
        for item in self.tree.get_children():
            item_text = self.tree.item(item, 'text').lower()
            if query_lower in item_text:
                self.tree.item(item, tags=('visible',))
            else:
                self.tree.item(item, tags=('hidden',))
        
        self.tree.tag_configure('hidden', foreground='gray')
        self.tree.tag_configure('visible', foreground='black')
    
    def handle_tree_events(self, event_type: str, event: tk.Event):
        """Handle various tree events"""
        print(f'[DEBUG] TreeManager.handle_tree_events called with event_type={event_type}')
        
        if event_type == 'click':
            self.on_tree_item_click(event)
        elif event_type == 'press':
            self.on_tree_item_press(event)
        elif event_type == 'drag':
            self.on_tree_item_drag(event)
        elif event_type == 'double_click':
            self.on_tree_double_click(event)
        elif event_type == 'context_menu':
            self.show_context_menu(event)

    def on_tree_item_press(self, event):
        """Handle mouse press on tree item for drag initiation"""
        print('[DEBUG] TreeManager.on_tree_item_press called')

        item = self.tree.identify_row(event.y)
        if item:
            item_values = self.tree.item(item, 'values')
            if item_values and item_values[0] == 'Clip Button':
                # Store drag source information
                self.drag_source = {
                    'item': item,
                    'type': 'clip_button',
                    'alias': self.tree.item(item, 'text').replace('📎 ', '').strip(),
                    'start_x': event.x,
                    'start_y': event.y
                }
                print(f'[DEBUG] Drag initiated for clip button: {self.drag_source["alias"]}')
            else:
                self.drag_source = None

    def on_tree_item_drag(self, event):
        """Handle drag motion"""
        if hasattr(self, 'drag_source') and self.drag_source:
            # Calculate drag distance
            dx = abs(event.x - self.drag_source['start_x'])
            dy = abs(event.y - self.drag_source['start_y'])

            # Only start visual drag feedback if moved enough
            if dx > 5 or dy > 5:
                # Change cursor to indicate dragging
                self.tree.config(cursor="hand2")

                # Highlight potential drop targets
                target_item = self.tree.identify_row(event.y)
                if target_item and target_item != self.drag_source['item']:
                    target_values = self.tree.item(target_item, 'values')
                    if target_values and target_values[0] in ['Business Case', 'Component']:
                        # Valid drop target
                        self.tree.selection_set(target_item)

    def on_tree_item_click(self, event):
        """Handle mouse release for drag completion"""
        print('[DEBUG] TreeManager.on_tree_item_click called')

        # Reset cursor
        self.tree.config(cursor="")

        if hasattr(self, 'drag_source') and self.drag_source:
            target_item = self.tree.identify_row(event.y)

            if target_item and target_item != self.drag_source['item']:
                target_values = self.tree.item(target_item, 'values')
                target_text = self.tree.item(target_item, 'text')

                if target_values and target_values[0] in ['Business Case', 'Component']:
                    # Valid drop - show context menu
                    self._show_drag_context_menu(event, target_item, target_values[0], target_text)

            # Clear drag source
            self.drag_source = None
        else:
            # Regular click handling
            item = self.tree.identify_row(event.y)
            if item:
                item_type = self.tree.item(item, 'values')[0] if self.tree.item(item, 'values') else None
                name = self.tree.item(item, 'text')

                # Notify parent of selection change
                if hasattr(self.parent, 'on_tree_selection_changed'):
                    self.parent.on_tree_selection_changed(item, item_type, name)

    def on_tree_double_click(self, event):
        """Handle double-click on tree items, especially clip buttons"""
        print('[DEBUG] TreeManager.on_tree_double_click called')

        item = self.tree.identify_row(event.y)
        if not item:
            return

        item_values = self.tree.item(item, 'values')
        item_text = self.tree.item(item, 'text')

        # Check if it's a clip button
        if item_values and item_values[0] == 'Clip Button':
            # Extract alias from the text (remove the 📎 emoji)
            alias = item_text.replace('📎 ', '').strip()
            self._copy_clip_by_alias(alias)

    def show_context_menu(self, event):
        """Show context menu on right-click"""
        print('[DEBUG] TreeManager.show_context_menu called')

        item = self.tree.identify_row(event.y)
        if item:
            self.tree.selection_set(item)
            self.context_menu = tk.Menu(self.parent, tearoff=0,
                                      bg=self.theme_manager.button_bg,
                                      fg=self.theme_manager.button_fg,
                                      activebackground=self.theme_manager.tree_select,
                                      activeforeground=self.theme_manager.fg_color)
            self.context_menu.add_command(label="Move", command=lambda: self.on_move(item))
            self.context_menu.add_command(label="Copy", command=lambda: self.on_copy(item))
            self.context_menu.post(event.x_root, event.y_root)

    def _show_drag_context_menu(self, event, target_item, target_type: str, target_name: str):
        """Show context menu for drag and drop operations"""
        print(f'[DEBUG] TreeManager._show_drag_context_menu called for {target_type}: {target_name}')

        if not hasattr(self, 'drag_source') or not self.drag_source:
            return

        alias = self.drag_source['alias']

        # Create context menu
        context_menu = tk.Menu(self.parent, tearoff=0,
                              bg=self.theme_manager.button_bg,
                              fg=self.theme_manager.button_fg,
                              activebackground=self.theme_manager.tree_select,
                              activeforeground=self.theme_manager.fg_color)

        context_menu.add_command(
            label=f"Move '{alias}' to {target_name}",
            command=lambda: self._handle_drag_move(alias, target_item, target_type, target_name)
        )

        context_menu.add_command(
            label=f"Copy '{alias}' to {target_name}",
            command=lambda: self._handle_drag_copy(alias, target_item, target_type, target_name)
        )

        context_menu.add_separator()

        context_menu.add_command(
            label="Cancel",
            command=lambda: context_menu.destroy()
        )

        # Show menu at cursor position
        try:
            context_menu.post(event.x_root, event.y_root)
        except tk.TclError:
            pass  # Menu might be destroyed already

    def _handle_drag_move(self, alias: str, target_item, target_type: str, target_name: str):
        """Handle move operation from drag and drop"""
        print(f'[DEBUG] TreeManager._handle_drag_move called: {alias} -> {target_name}')

        try:
            more_ops = self.database_manager.get_more_operations()
            enhanced_ops = self.database_manager.get_enhanced_operations()

            if target_type == 'Business Case':
                target_bus_id = more_ops.get_business_case_id_by_name(target_name)
                target_comp_id = None
            else:  # Component
                # Find parent business case
                parent_item = self.tree.parent(target_item)
                parent_name = self.tree.item(parent_item, 'text')
                target_bus_id = more_ops.get_business_case_id_by_name(parent_name)
                target_comp_id = more_ops.get_component_id_by_name_in_business_case(target_name, target_bus_id)

            # Get transaction ID for the alias
            import sqlite3
            with sqlite3.connect("source/DB/clipsmore_db.db") as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT transaction_id FROM clipsmore_tbl WHERE alias = ?", (alias,))
                result = cursor.fetchone()

                if result:
                    transaction_id = result[0]

                    # Perform move operation
                    success = enhanced_ops.move_assignment(transaction_id, target_bus_id, target_comp_id)

                    if success:
                        print(f'[DEBUG] Successfully moved {alias} to {target_name}')
                        self.refresh_tree()
                        messagebox.showinfo("Move Successful", f"Moved '{alias}' to {target_name}")
                    else:
                        messagebox.showerror("Move Failed", f"Failed to move '{alias}' to {target_name}")
                else:
                    messagebox.showerror("Move Failed", f"Assignment for '{alias}' not found")

        except Exception as e:
            print(f'[ERROR] Drag move failed: {e}')
            messagebox.showerror("Move Failed", f"Error moving clip: {e}")

    def _handle_drag_copy(self, alias: str, target_item, target_type: str, target_name: str):
        """Handle copy operation from drag and drop"""
        print(f'[DEBUG] TreeManager._handle_drag_copy called: {alias} -> {target_name}')

        try:
            more_ops = self.database_manager.get_more_operations()
            enhanced_ops = self.database_manager.get_enhanced_operations()

            if target_type == 'Business Case':
                target_bus_id = more_ops.get_business_case_id_by_name(target_name)
                target_comp_id = None
            else:  # Component
                # Find parent business case
                parent_item = self.tree.parent(target_item)
                parent_name = self.tree.item(parent_item, 'text')
                target_bus_id = more_ops.get_business_case_id_by_name(parent_name)
                target_comp_id = more_ops.get_component_id_by_name_in_business_case(target_name, target_bus_id)

            # Get transaction ID for the alias
            import sqlite3
            with sqlite3.connect("source/DB/clipsmore_db.db") as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT transaction_id FROM clipsmore_tbl WHERE alias = ?", (alias,))
                result = cursor.fetchone()

                if result:
                    transaction_id = result[0]

                    # Perform copy operation
                    new_transaction_id = enhanced_ops.copy_assignment(transaction_id, target_bus_id, target_comp_id)

                    if new_transaction_id:
                        print(f'[DEBUG] Successfully copied {alias} to {target_name}')
                        self.refresh_tree()
                        messagebox.showinfo("Copy Successful", f"Copied '{alias}' to {target_name}")
                    else:
                        messagebox.showerror("Copy Failed", f"Failed to copy '{alias}' to {target_name}")
                else:
                    messagebox.showerror("Copy Failed", f"Assignment for '{alias}' not found")

        except Exception as e:
            print(f'[ERROR] Drag copy failed: {e}')
            messagebox.showerror("Copy Failed", f"Error copying clip: {e}")

    def on_move(self, item):
        """Handle move operation from context menu"""
        print(f'[DEBUG] TreeManager.on_move called with item={item}')
        # This is for the right-click context menu, different from drag & drop
        print(f"Moving item: {self.tree.item(item, 'text')}")

    def on_copy(self, item):
        """Handle copy operation from context menu"""
        print(f'[DEBUG] TreeManager.on_copy called with item={item}')
        # Implement copy logic here
        print(f"Copying item: {self.tree.item(item, 'text')}")

    def get_selected_item(self) -> Optional[str]:
        """Get currently selected tree item"""
        return self.tree.focus() if self.tree else None

    def get_item_info(self, item: str) -> Dict[str, Any]:
        """Get information about a tree item"""
        if not item or not self.tree:
            return {}

        return {
            'text': self.tree.item(item, 'text'),
            'values': self.tree.item(item, 'values'),
            'parent': self.tree.parent(item),
            'children': self.tree.get_children(item)
        }
