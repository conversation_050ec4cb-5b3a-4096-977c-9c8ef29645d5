"""
🗄️ DatabaseManager - Centralizes all database operations and connections

This manager is responsible for:
- Providing centralized access to all database operation classes
- Managing database connections and error handling
- Executing database operations with proper error handling
- Providing helper methods for common database queries
- Caching frequently accessed data

Part of the UIManager refactoring to improve code organization and maintainability.
"""

import sqlite3
from typing import List, Dict, Optional, Any, Callable
from functools import lru_cache


class DatabaseManager:
    """Centralizes all database operations and provides error handling"""
    
    def __init__(self):
        """Initialize DatabaseManager"""
        print('[DEBUG] DatabaseManager.__init__ called')
        
        self.db_path = "source/DB/clipsmore_db.db"
        
        # Cache for operation instances
        self._clips_ops = None
        self._more_ops = None
        self._enhanced_ops = None
        self._count_ops = None
        self._view_ops = None
        
        # Cache for frequently accessed data
        self._existing_aliases_cache = None
        self._cache_timestamp = None
    
    def get_clips_operations(self):
        """Get ClipsTableOperations instance"""
        print('[DEBUG] DatabaseManager.get_clips_operations called')
        
        if self._clips_ops is None:
            from source.DB.op_clips_tbl import ClipsTableOperations
            self._clips_ops = ClipsTableOperations()
        
        return self._clips_ops
    
    def get_more_operations(self):
        """Get MoreTableOperations instance"""
        print('[DEBUG] DatabaseManager.get_more_operations called')
        
        if self._more_ops is None:
            from source.DB.op_more_tbl import MoreTableOperations
            self._more_ops = MoreTableOperations()
        
        return self._more_ops
    
    def get_enhanced_operations(self):
        """Get ClipsMoreEnhancedOperations instance"""
        print('[DEBUG] DatabaseManager.get_enhanced_operations called')
        
        if self._enhanced_ops is None:
            from source.DB.op_clipsmore_enhanced import ClipsMoreEnhancedOperations
            self._enhanced_ops = ClipsMoreEnhancedOperations()
        
        return self._enhanced_ops
    
    def get_count_operations(self):
        """Get ClipsMoreCountOperations instance"""
        print('[DEBUG] DatabaseManager.get_count_operations called')
        
        if self._count_ops is None:
            from source.DB.op_clipsmore_count import ClipsMoreCountOperations
            self._count_ops = ClipsMoreCountOperations()
        
        return self._count_ops
    
    def get_view_operations(self):
        """Get ClipsMoreViewOperations instance"""
        print('[DEBUG] DatabaseManager.get_view_operations called')
        
        if self._view_ops is None:
            from source.DB.op_clipsmore_vw import ClipsMoreViewOperations
            self._view_ops = ClipsMoreViewOperations()
        
        return self._view_ops
    
    def execute_with_error_handling(self, operation: Callable, *args, **kwargs) -> Any:
        """Execute database operation with comprehensive error handling"""
        print(f'[DEBUG] DatabaseManager.execute_with_error_handling called for operation={operation.__name__}')
        
        try:
            result = operation(*args, **kwargs)
            print(f'[DEBUG] Operation {operation.__name__} completed successfully')
            return result
            
        except sqlite3.IntegrityError as e:
            print(f'[ERROR] Database integrity error in {operation.__name__}: {e}')
            raise Exception(f"Database integrity error: {str(e)}")
            
        except sqlite3.OperationalError as e:
            print(f'[ERROR] Database operational error in {operation.__name__}: {e}')
            raise Exception(f"Database operational error: {str(e)}")
            
        except sqlite3.DatabaseError as e:
            print(f'[ERROR] Database error in {operation.__name__}: {e}')
            raise Exception(f"Database error: {str(e)}")
            
        except Exception as e:
            print(f'[ERROR] Unexpected error in {operation.__name__}: {e}')
            raise Exception(f"Unexpected error: {str(e)}")
    
    def get_existing_aliases(self, force_refresh: bool = False) -> List[str]:
        """Get all existing aliases from database with caching"""
        print(f'[DEBUG] DatabaseManager.get_existing_aliases called with force_refresh={force_refresh}')
        
        import time
        current_time = time.time()
        
        # Use cache if available and not expired (cache for 30 seconds)
        if (not force_refresh and 
            self._existing_aliases_cache is not None and 
            self._cache_timestamp is not None and 
            current_time - self._cache_timestamp < 30):
            print('[DEBUG] Returning cached aliases')
            return self._existing_aliases_cache
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT alias FROM clipsmore_tbl WHERE alias IS NOT NULL")
                aliases = [row[0] for row in cursor.fetchall()]
                
                # Update cache
                self._existing_aliases_cache = aliases
                self._cache_timestamp = current_time
                
                print(f'[DEBUG] Retrieved {len(aliases)} existing aliases from database')
                return aliases
                
        except Exception as e:
            print(f'[ERROR] Failed to get existing aliases: {e}')
            return []
    
    def get_business_case_id_by_name(self, name: str) -> Optional[int]:
        """Get business case ID by name"""
        print(f'[DEBUG] DatabaseManager.get_business_case_id_by_name called with name={name}')
        
        try:
            more_ops = self.get_more_operations()
            return more_ops.get_business_case_id_by_name(name)
            
        except Exception as e:
            print(f'[ERROR] Failed to get business case ID by name: {e}')
            return None
    
    def get_component_id_by_name(self, name: str) -> Optional[int]:
        """Get component ID by name"""
        print(f'[DEBUG] DatabaseManager.get_component_id_by_name called with name={name}')
        
        try:
            more_ops = self.get_more_operations()
            return more_ops.get_component_id_by_name(name)
            
        except Exception as e:
            print(f'[ERROR] Failed to get component ID by name: {e}')
            return None
    
    def get_component_id_by_name_in_business_case(self, comp_name: str, bus_id: int) -> Optional[int]:
        """Get component ID by name within a specific business case"""
        print(f'[DEBUG] DatabaseManager.get_component_id_by_name_in_business_case called with comp_name={comp_name}, bus_id={bus_id}')
        
        try:
            more_ops = self.get_more_operations()
            return more_ops.get_component_id_by_name_in_business_case(comp_name, bus_id)
            
        except Exception as e:
            print(f'[ERROR] Failed to get component ID by name in business case: {e}')
            return None
    
    def get_business_case_id_of_component(self, comp_id: int) -> Optional[int]:
        """Get business case ID that contains the specified component"""
        print(f'[DEBUG] DatabaseManager.get_business_case_id_of_component called with comp_id={comp_id}')
        
        try:
            more_ops = self.get_more_operations()
            return more_ops.get_business_case_id_of_component(comp_id)
            
        except Exception as e:
            print(f'[ERROR] Failed to get business case ID of component: {e}')
            return None
    
    def get_clip_content_by_alias(self, alias: str) -> Optional[str]:
        """Get clip content by alias using the view"""
        print(f'[DEBUG] DatabaseManager.get_clip_content_by_alias called with alias={alias}')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT clip_content
                    FROM clipsmore_vw
                    WHERE alias = ?
                    LIMIT 1
                """, (alias,))
                
                result = cursor.fetchone()
                if result:
                    content = result[0]
                    print(f'[DEBUG] Found content for alias: {alias}')
                    return content
                else:
                    print(f'[WARNING] No content found for alias: {alias}')
                    return None
                    
        except Exception as e:
            print(f'[ERROR] Failed to get clip content by alias: {e}')
            return None
    
    def get_assignment_by_alias(self, alias: str) -> Optional[Dict[str, Any]]:
        """Get assignment information by alias"""
        print(f'[DEBUG] DatabaseManager.get_assignment_by_alias called with alias={alias}')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT transaction_id, clip_id, more_bus_id, more_comp_id, alias
                    FROM clipsmore_tbl
                    WHERE alias = ?
                    LIMIT 1
                """, (alias,))
                
                result = cursor.fetchone()
                if result:
                    assignment = {
                        'transaction_id': result[0],
                        'clip_id': result[1],
                        'more_bus_id': result[2],
                        'more_comp_id': result[3],
                        'alias': result[4]
                    }
                    print(f'[DEBUG] Found assignment for alias: {alias}')
                    return assignment
                else:
                    print(f'[WARNING] No assignment found for alias: {alias}')
                    return None
                    
        except Exception as e:
            print(f'[ERROR] Failed to get assignment by alias: {e}')
            return None
    
    def clear_cache(self):
        """Clear all cached data"""
        print('[DEBUG] DatabaseManager.clear_cache called')
        
        self._existing_aliases_cache = None
        self._cache_timestamp = None
    
    def get_database_stats(self) -> Dict[str, int]:
        """Get database statistics"""
        print('[DEBUG] DatabaseManager.get_database_stats called')
        
        stats = {
            'clips_count': 0,
            'business_cases_count': 0,
            'components_count': 0,
            'assignments_count': 0
        }
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Count clips
                cursor.execute("SELECT COUNT(*) FROM clips_tbl")
                stats['clips_count'] = cursor.fetchone()[0]
                
                # Count business cases
                cursor.execute("SELECT COUNT(*) FROM more_tbl WHERE more_comp_id IS NULL")
                stats['business_cases_count'] = cursor.fetchone()[0]
                
                # Count components
                cursor.execute("SELECT COUNT(*) FROM more_tbl WHERE more_comp_id IS NOT NULL")
                stats['components_count'] = cursor.fetchone()[0]
                
                # Count assignments
                cursor.execute("SELECT COUNT(*) FROM clipsmore_tbl")
                stats['assignments_count'] = cursor.fetchone()[0]
                
                print(f'[DEBUG] Database stats: {stats}')
                return stats
                
        except Exception as e:
            print(f'[ERROR] Failed to get database stats: {e}')
            return stats

    def truncate_all_tables(self, confirmation: str) -> bool:
        """Truncate ALL tables in the database with confirmation.

        This is a destructive operation that removes all data from all tables
        including clips, business cases, components, assignments, export templates,
        backup history, and import history.

        Args:
            confirmation: Must be "CONFIRM_TRUNCATE_ALL" to proceed

        Returns:
            bool: True if successful, False otherwise
        """
        print(f'[DEBUG] DatabaseManager.truncate_all_tables called with confirmation: {confirmation}')

        if confirmation != "CONFIRM_TRUNCATE_ALL":
            print('[ERROR] Invalid confirmation for truncate all tables')
            return False

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Disable foreign key constraints temporarily
                cursor.execute("PRAGMA foreign_keys = OFF")

                # List of all tables to truncate in dependency order
                # (child tables first to avoid foreign key issues)
                tables_to_truncate = [
                    'clipsmore_tbl',      # Assignments (references clips, business cases, components)
                    'more_comp_tbl',      # Components (references business cases)
                    'clips_tbl',          # Clips
                    'more_bus_tbl',       # Business cases
                    'export_templates',   # Export templates
                    'backup_history',     # Backup history
                    'import_history'      # Import history
                ]

                total_deleted = 0

                for table_name in tables_to_truncate:
                    try:
                        # Check if table exists first
                        cursor.execute("""
                            SELECT name FROM sqlite_master
                            WHERE type='table' AND name=?
                        """, (table_name,))

                        if cursor.fetchone():
                            # Get count before deletion for logging
                            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                            count_before = cursor.fetchone()[0]

                            # Truncate the table
                            cursor.execute(f"DELETE FROM {table_name}")
                            deleted_count = cursor.rowcount
                            total_deleted += deleted_count

                            print(f'[DEBUG] Truncated {table_name}: {deleted_count} rows deleted (was {count_before})')
                        else:
                            print(f'[DEBUG] Table {table_name} does not exist, skipping')

                    except sqlite3.Error as e:
                        print(f'[ERROR] Failed to truncate table {table_name}: {e}')
                        # Continue with other tables even if one fails

                # Re-enable foreign key constraints
                cursor.execute("PRAGMA foreign_keys = ON")

                # Commit all changes
                conn.commit()

                print(f'[DEBUG] Successfully truncated all tables. Total rows deleted: {total_deleted}')
                return True

        except Exception as e:
            print(f'[ERROR] Failed to truncate all tables: {e}')
            return False
    
    def test_connection(self) -> bool:
        """Test database connection"""
        print('[DEBUG] DatabaseManager.test_connection called')
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                if result and result[0] == 1:
                    print('[DEBUG] Database connection test successful')
                    return True
                else:
                    print('[ERROR] Database connection test failed - unexpected result')
                    return False
                    
        except Exception as e:
            print(f'[ERROR] Database connection test failed: {e}')
            return False
    
    def backup_database(self, backup_path: str) -> bool:
        """Create a backup of the database"""
        print(f'[DEBUG] DatabaseManager.backup_database called with backup_path={backup_path}')
        
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            print(f'[DEBUG] Database backed up to: {backup_path}')
            return True
            
        except Exception as e:
            print(f'[ERROR] Database backup failed: {e}')
            return False
